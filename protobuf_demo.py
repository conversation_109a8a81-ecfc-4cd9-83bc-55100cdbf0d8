# /// script
# requires-python = ">=3.11"
# dependencies = ["grpclib>=0.4.7,<0.4.9", "grpcio-tools", "requests", "protobuf>=3.19,<7.0,!=4.24.0"]
# ///

#!/usr/bin/env python3
"""Modal protobuf demo - professional API functions (Self-contained)

SETUP INSTRUCTIONS (One-time setup):
=====================================

1. Clone Modal client repo:
   git clone https://github.com/modal-labs/modal-client.git
   cd modal-client

2. Generate protobuf files (REQUIRED - run these commands in modal-client directory):

   # Quick setup (copy-paste this):
   cd /path/to/modal-client && uv add grpcio-tools && uv run python -m grpc_tools.protoc --python_out=. --grpclib_python_out=. --grpc_python_out=. -I . modal_proto/api.proto modal_proto/options.proto

   # OR step by step:
   uv add grpcio-tools
   uv run python -m grpc_tools.protoc --python_out=. --grpclib_python_out=. --grpc_python_out=. -I . modal_proto/api.proto modal_proto/options.proto

   # OR if you have the full dev environment:
   pip install invoke grpcio-tools grpclib
   inv protoc

3. Update hardcoded credentials in this script:
   - Get your credentials: modal token new (or check ~/.modal.toml)
   - Update TOKEN_ID and TOKEN_SECRET variables below

4. (Optional) Create a test volume if you don't have any:
   modal volume create test-volume

5. Update MODAL_CLIENT_REPO_PATH below to point to your modal-client repo checkout

6. Run this script:
   uv run protobuf_demo.py

   The script will automatically use the first available volume.

PROTOBUF FILES REQUIRED:
========================
This script needs these generated files in modal_proto/:
- api_pb2.py
- api_pb2_grpc.py
- options_pb2.py
- options_pb2_grpc.py

If missing, run the protoc command above in the modal-client directory.

This script provides high-level functions that map to Modal's protobuf APIs:
- list_volumes(): Get all volumes
- list_files(): Get files in a volume
- put_file(): Upload file to volume
- get_file(): Download file from volume
- remove_file(): Delete file from volume
- copy_files(): Copy files within volume

All functions include built-in retry logic for transient failures, automatically handling:
- Network timeouts (DEADLINE_EXCEEDED)
- Service unavailable (UNAVAILABLE)
- Request cancellation (CANCELLED)
- Internal server errors (INTERNAL)
- Unknown errors (UNKNOWN)

The retry mechanism follows Modal's official client patterns:
- Exponential backoff with jitter
- Unique idempotency keys per logical request
- Proper x-retry-attempt header incrementing
- x-retry-delay tracking for observability

Protobuf API Reference (modal_proto/api.proto):
=====================================================

Volume Service RPCs:
- VolumeList(VolumeListRequest) → VolumeListResponse
- VolumeListFiles(VolumeListFilesRequest) → stream VolumeListFilesResponse
- VolumeGetFile2(VolumeGetFile2Request) → VolumeGetFile2Response
- VolumePutFiles(VolumePutFilesRequest) → Empty
- VolumeRemoveFile(VolumeRemoveFileRequest) → Empty
- VolumeCopyFiles(VolumeCopyFilesRequest) → Empty
- VolumeCommit(VolumeCommitRequest) → VolumeCommitResponse
- VolumeGetOrCreate(VolumeGetOrCreateRequest) → VolumeGetOrCreateResponse
- VolumeHeartbeat(VolumeHeartbeatRequest) → Empty

Mount Service RPCs (for file uploads):
- MountPutFile(MountPutFileRequest) → MountPutFileResponse

Key Message Types:
- VolumeListRequest: {} (empty)
- VolumeListResponse: { repeated VolumeListItem items }
- VolumeListItem: { string label, string volume_id, double created_at }
- VolumeListFilesRequest: { string volume_id, string path, bool recursive }
- VolumeListFilesResponse: { repeated FileEntry entries }
- VolumeGetFile2Request: { string volume_id, string path, uint64 start, uint64 len }
- VolumeGetFile2Response: { repeated string get_urls, uint64 size, uint64 start, uint64 len }
- VolumePutFilesRequest: { string volume_id, repeated MountFile files, bool disallow_overwrite_existing_files }
- MountFile: { string filename, string sha256_hex, uint64 size, uint32 mode }
- MountPutFileRequest: { string sha256_hex, bytes data }
- MountPutFileResponse: { bool exists }
- VolumeRemoveFileRequest: { string volume_id, string path, bool recursive }
- VolumeCopyFilesRequest: { string volume_id, repeated string src_paths, string dst_path, bool recursive }

Example Usage of Individual API Functions:
==========================================

# Setup connection
token_id, token_secret = load_credentials()
import grpc
from modal_proto import api_pb2_grpc

channel = grpc.secure_channel('api.modal.com:443', grpc.ssl_channel_credentials())
stub = api_pb2_grpc.ModalClientStub(channel)

# List all volumes (with built-in retry logic)
volumes = list_volumes(stub, token_id, token_secret)
volume_id = volumes[0].volume_id  # Use first volume

# Upload a file (with built-in retry logic)
put_file(
    stub=stub,
    token_id=token_id,
    token_secret=token_secret,
    volume_id=volume_id,
    local_path="/path/to/local/file.txt",
    remote_path="uploaded_file.txt",
    mode=0o644,
    allow_overwrite=True
)

# List files (with built-in retry logic)
files = list_files(
    stub=stub,
    token_id=token_id,
    token_secret=token_secret,
    volume_id=volume_id,
    path="/",
    recursive=True
)

# Download a file (with built-in retry logic)
result = get_file(
    stub=stub,
    token_id=token_id,
    token_secret=token_secret,
    volume_id=volume_id,
    remote_path="uploaded_file.txt",
    start=0,
    length=None
)

# Copy files (with built-in retry logic)
copy_files(
    stub=stub,
    token_id=token_id,
    token_secret=token_secret,
    volume_id=volume_id,
    src_paths=["uploaded_file.txt"],
    dst_path="copied_file.txt",
    recursive=False
)

# Remove a file (with built-in retry logic)
remove_file(
    stub=stub,
    token_id=token_id,
    token_secret=token_secret,
    volume_id=volume_id,
    remote_path="uploaded_file.txt",
    recursive=False
)

channel.close()

=== Complete Modal Volume Protobuf API Reference ===

This section provides comprehensive protobuf definitions from modal_proto/api.proto
for all volume-related operations used in this demo.

Service Definition:
service ModalClient {
  // Volume Management
  rpc VolumeList(VolumeListRequest) returns (VolumeListResponse);
  rpc VolumeListFiles(VolumeListFilesRequest) returns (stream VolumeListFilesResponse);
  rpc VolumeGetFile2(VolumeGetFile2Request) returns (VolumeGetFile2Response);
  rpc VolumePutFiles(VolumePutFilesRequest) returns (google.protobuf.Empty);
  rpc VolumeRemoveFile(VolumeRemoveFileRequest) returns (google.protobuf.Empty);
  rpc VolumeCopyFiles(VolumeCopyFilesRequest) returns (google.protobuf.Empty);
  rpc VolumeCommit(VolumeCommitRequest) returns (VolumeCommitResponse);
  rpc VolumeGetOrCreate(VolumeGetOrCreateRequest) returns (VolumeGetOrCreateResponse);
  rpc VolumeHeartbeat(VolumeHeartbeatRequest) returns (google.protobuf.Empty);

  // Mount Management (for file uploads)
  rpc MountPutFile(MountPutFileRequest) returns (MountPutFileResponse);
}

Message Definitions:

// === Volume Listing ===
message VolumeListRequest {
  string environment_name = 1;
}

message VolumeListResponse {
  repeated VolumeListItem items = 1;
  string environment_name = 2;
}

message VolumeListItem {
  string label = 1;           // Volume name/label
  string volume_id = 2;       // Unique volume identifier
  double created_at = 3;      // Creation timestamp
}

// === File Listing ===
message VolumeListFilesRequest {
  string volume_id = 1;       // Target volume
  string path = 2;            // Starting path (e.g., "/")
  bool recursive = 4;         // Include subdirectories
  optional uint32 max_entries = 3;  // Limit number of results
}

message VolumeListFilesResponse {
  repeated FileEntry entries = 1;
}

message FileEntry {
  string path = 1;            // File/directory path
  uint64 size = 2;            // Size in bytes (0 for directories)
  FileType type = 3;          // FILE=1, DIRECTORY=2
  // Additional fields: permissions, timestamps, etc.
}

// === File Download ===
message VolumeGetFile2Request {
  string volume_id = 1;       // Source volume
  string path = 2;            // File path to download
  uint64 start = 3;           // Byte offset (for partial downloads)
  uint64 len = 4;             // Number of bytes (0 = entire file)
}

message VolumeGetFile2Response {
  repeated string get_urls = 1;  // Signed URLs for HTTP download
  uint64 size = 2;               // Total file size
  uint64 start = 3;              // Starting byte position
  uint64 len = 4;                // Number of bytes in response
}

// === File Upload ===
message VolumePutFilesRequest {
  string volume_id = 1;                           // Target volume
  repeated MountFile files = 2;                   // Files to upload
  bool disallow_overwrite_existing_files = 3;    // Prevent overwrites
}

message MountFile {
  string filename = 1;        // Destination path in volume
  string sha256_hex = 3;      // SHA-256 hash (for deduplication)
  optional uint64 size = 4;   // File size in bytes
  optional uint32 mode = 5;   // Unix permissions (e.g., 0o644)
}

message MountPutFileRequest {
  string sha256_hex = 2;      // Content hash
  oneof data_oneof {
    bytes data = 3;           // File content (for upload)
    string data_blob_id = 5;  // Alternative: blob reference
  }
}

message MountPutFileResponse {
  bool exists = 2;            // True if content already exists (deduplication)
}

// === File Removal ===
message VolumeRemoveFileRequest {
  string volume_id = 1;       // Target volume
  string path = 2;            // File/directory to remove
  bool recursive = 3;         // Remove directories recursively
}

// === File Copying ===
message VolumeCopyFilesRequest {
  string volume_id = 1;       // Target volume
  repeated string src_paths = 2;  // Source paths to copy
  string dst_path = 3;        // Destination path
  bool recursive = 4;         // Copy directories recursively
}

Authentication Headers:
All requests require these metadata headers:
- x-modal-token-id: Your Modal token ID
- x-modal-token-secret: Your Modal token secret
- x-modal-client-version: Modal client version
- x-modal-client-type: "1" (CLIENT_TYPE_CLIENT)
- x-modal-python-version: Python version string
- x-modal-node: URL-encoded hostname
- x-modal-platform: URL-encoded platform info
- x-idempotency-key: Unique UUID per logical request (stays same across retries)
- x-retry-attempt: Current attempt number ("0" for first attempt, "1" for first retry, etc.)
- x-retry-delay: Total time elapsed since first attempt (only present on retries)

Retry Logic Implementation:
The demo script implements the same retry logic as Modal's official client:
1. Each logical request gets a unique x-idempotency-key that persists across retries
2. x-retry-attempt increments for each retry attempt (0, 1, 2, 3...)
3. x-retry-delay tracks elapsed time since the original request
4. Exponential backoff: delays increase by 2x each retry (0.1s, 0.2s, 0.4s, 0.8s...)
5. Maximum of 3 retries by default for transient errors
6. Only retries on specific gRPC status codes: DEADLINE_EXCEEDED, UNAVAILABLE, CANCELLED, INTERNAL, UNKNOWN

Content Deduplication:
Modal uses SHA-256 hashing for content deduplication. Before uploading file content:
1. Calculate SHA-256 hash of file content
2. Call MountPutFile with just the hash to check if content exists
3. Only upload content if MountPutFileResponse.exists == false
4. Register the file in the volume using VolumePutFiles with the hash

File Downloads:
VolumeGetFile2 returns signed URLs rather than file content directly:
1. Call VolumeGetFile2 to get signed download URLs
2. Use HTTP GET requests to download actual content from these URLs
3. URLs are time-limited and single-use for security

Error Handling:
- gRPC errors indicate API-level failures (authentication, invalid requests)
- HTTP errors from download URLs indicate network/storage issues
- Empty responses generally indicate success for operations without return values
"""

import hashlib
import os
import platform
import sys
import tempfile
import time
import urllib.parse
import uuid
from typing import Callable, List, Optional

# Update this path to point to your modal-client git checkout
MODAL_CLIENT_REPO_PATH = "/home/<USER>/workspace/modal-client"  # Change this to your repo checkout path
sys.path.insert(0, MODAL_CLIENT_REPO_PATH)

# Import version directly to avoid Modal import issues
from modal_version import __version__

# Retryable gRPC status codes (from Modal's grpc_utils.py)
try:
    import grpc

    RETRYABLE_GRPC_STATUS_CODES = [
        grpc.StatusCode.DEADLINE_EXCEEDED,
        grpc.StatusCode.UNAVAILABLE,
        grpc.StatusCode.CANCELLED,
        grpc.StatusCode.INTERNAL,
        grpc.StatusCode.UNKNOWN,
    ]
except ImportError:
    RETRYABLE_GRPC_STATUS_CODES = []


def check_protobuf_files():
    """Check if required protobuf files exist and provide setup instructions if missing."""
    required_files = [
        "modal_proto/api_pb2.py",
        "modal_proto/api_pb2_grpc.py",
        "modal_proto/options_pb2.py",
        "modal_proto/options_pb2_grpc.py",
    ]

    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(MODAL_CLIENT_REPO_PATH, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)

    if missing_files:
        print("ERROR: Missing required protobuf files!")
        print("\nMissing files:")
        for file in missing_files:
            print(f"   - {file}")

        print(f"\nTO FIX: Run this command in the modal-client directory ({MODAL_CLIENT_REPO_PATH}):")
        print(
            "   uv run python -m grpc_tools.protoc --python_out=. --grpclib_python_out=. --grpc_python_out=. -I . modal_proto/api.proto modal_proto/options.proto"
        )
        print("\n   OR if you have invoke installed:")
        print("   inv protoc")
        return False

    print("All required protobuf files found")
    return True


# HARDCODED CREDENTIALS - UPDATE THESE WITH YOUR MODAL TOKEN
TOKEN_ID = "ak-XfH32vJEkvb0d23D3dk0s4"
TOKEN_SECRET = "as-EjqLefVxL9aAYnKj8PiPZI"


def load_credentials():
    """Load hardcoded Modal credentials.

    Returns:
        tuple: (token_id, token_secret) or (None, None) if not configured
    """
    if TOKEN_ID == "your-token-id-here" or TOKEN_SECRET == "your-token-secret-here":
        print("ERROR: Hardcoded credentials not configured!")
        print("TO FIX: Update TOKEN_ID and TOKEN_SECRET variables in this script")
        print("Get your credentials from ~/.modal.toml or run 'modal token new'")
        return None, None

    print("Hardcoded Modal credentials loaded")
    return TOKEN_ID, TOKEN_SECRET


def retry_transient_errors(
    fn: Callable,
    *args,
    base_delay: float = 0.1,
    max_delay: float = 1.0,
    delay_factor: float = 2.0,
    max_retries: Optional[int] = 3,
    additional_status_codes: Optional[List] = None,
    **kwargs,
):
    """Retry on transient gRPC failures with back-off until max_retries is reached.

    This implements the same retry logic as Modal's official client.

    Args:
        fn: Function to retry
        *args: Arguments to pass to function
        base_delay: Initial delay between retries
        max_delay: Maximum delay between retries
        delay_factor: Factor to multiply delay by after each retry
        max_retries: Maximum number of retries (None for infinite)
        additional_status_codes: Additional gRPC status codes to retry on
        **kwargs: Keyword arguments to pass to function

    Returns:
        The result of the successful function call
    """
    if additional_status_codes is None:
        additional_status_codes = []

    delay = base_delay
    n_retries = 0
    status_codes = RETRYABLE_GRPC_STATUS_CODES + additional_status_codes
    idempotency_key = str(uuid.uuid4())
    t0 = time.time()

    while True:
        # Build metadata with retry information
        if "metadata" in kwargs:
            # Modify existing metadata
            metadata_dict = dict(kwargs["metadata"])
        else:
            metadata_dict = {}

        # Update retry-specific headers
        metadata_dict.update(
            {
                "x-idempotency-key": idempotency_key,
                "x-retry-attempt": str(n_retries),
            }
        )

        if n_retries > 0:
            metadata_dict["x-retry-delay"] = str(time.time() - t0)

        # Convert back to tuple format for gRPC
        kwargs["metadata"] = tuple((k, v) for k, v in metadata_dict.items())

        try:
            return fn(*args, **kwargs)
        except Exception as exc:
            # Check if this is a retryable gRPC error
            is_retryable = False

            # Try different ways to get gRPC status code
            try:
                if hasattr(exc, "code") and callable(getattr(exc, "code", None)):
                    grpc_code = exc.code()  # type: ignore
                    is_retryable = grpc_code in status_codes
                elif hasattr(exc, "_state") and hasattr(getattr(exc, "_state", None), "code"):
                    # Different gRPC error format
                    grpc_code = exc._state.code  # type: ignore
                    is_retryable = grpc_code in status_codes
                elif hasattr(exc, "details") and "UNAVAILABLE" in str(exc):
                    # String-based detection for some gRPC errors
                    is_retryable = True
            except:
                # If we can't determine the gRPC code, check for network errors
                pass

            # Network errors are generally retryable
            if isinstance(exc, (OSError, ConnectionError)):
                is_retryable = True

            if not is_retryable:
                print(f"Non-retryable error: {exc}")
                raise exc

            if max_retries is not None and n_retries >= max_retries:
                print(f"Final attempt failed with {exc} after {n_retries} retries")
                raise exc

            print(f"Retryable failure {exc} (attempt {n_retries}, delay {delay:.2f}s)")
            n_retries += 1

            time.sleep(delay)
            delay = min(delay * delay_factor, max_delay)


def get_modal_metadata(token_id, token_secret, retry_attempt: int = 0, idempotency_key: Optional[str] = None):
    """Generate metadata exactly like the official Modal Python client.

    Args:
        token_id (str): Modal token ID
        token_secret (str): Modal token secret
        retry_attempt (int): Current retry attempt number (0 for first attempt)
        idempotency_key (str): Unique key for request idempotency (generated if None)

    Returns:
        tuple: Authentication metadata for gRPC calls
    """
    # Platform detection (same as official client)
    uname = platform.uname()
    if uname.system == "Darwin":
        system, release = "macOS", platform.mac_ver()[0]
    else:
        system, release = uname.system, uname.release
    platform_str = "-".join(s.replace("-", "_") for s in (system, release, uname.machine))

    python_version = "%d.%d.%d" % (sys.version_info.major, sys.version_info.minor, sys.version_info.micro)

    # Generate idempotency key if not provided
    if idempotency_key is None:
        idempotency_key = str(uuid.uuid4())

    # Official client metadata with required retry headers
    metadata = {
        "x-modal-client-version": __version__,  # Use actual modal version like tests
        "x-modal-client-type": "1",  # CLIENT_TYPE_CLIENT (tests use str(api_pb2.CLIENT_TYPE_CLIENT))
        "x-modal-python-version": python_version,
        "x-modal-node": urllib.parse.quote(platform.node()),
        "x-modal-platform": urllib.parse.quote(platform_str),
        "x-modal-token-id": token_id,
        "x-modal-token-secret": token_secret,
        # Critical headers that official client always sends
        "x-idempotency-key": idempotency_key,  # Unique for each logical request
        "x-retry-attempt": str(retry_attempt),  # Current attempt number
    }

    # Convert to tuple format for gRPC
    return tuple((k, v) for k, v in metadata.items())


def list_volumes(stub, token_id, token_secret):
    """List all volumes with retry logic.

    Protobuf API: VolumeList(VolumeListRequest) → VolumeListResponse
    Request: VolumeListRequest {} (empty message)
    Response: VolumeListResponse { repeated VolumeListItem items }
    VolumeListItem: { string label, string volume_id, double created_at }

    Args:
        stub: gRPC client stub
        token_id: Modal token ID
        token_secret: Modal token secret

    Returns:
        list: List of volume objects with id and label attributes
    """
    from modal_proto import api_pb2

    def _make_request(metadata):
        request = api_pb2.VolumeListRequest()
        response = stub.VolumeList(request, metadata=metadata)
        return response.items

    # Generate base metadata and use retry logic
    base_metadata = get_modal_metadata(token_id, token_secret, retry_attempt=0)
    return retry_transient_errors(_make_request, metadata=base_metadata)


def list_files(stub, token_id, token_secret, volume_id, path="/", recursive=True):
    """List files in a volume with retry logic.

    Protobuf API: VolumeListFiles(VolumeListFilesRequest) → stream VolumeListFilesResponse
    Request: VolumeListFilesRequest { string volume_id, string path, bool recursive, optional uint32 max_entries }
    Response: stream VolumeListFilesResponse { repeated FileEntry entries }
    FileEntry: { string path, uint64 size, FileType type, ... }

    Args:
        stub: gRPC client stub
        token_id: Modal token ID
        token_secret: Modal token secret
        volume_id (str): Volume ID to list files from
        path (str): Path to list files from (default: "/")
        recursive (bool): Whether to list files recursively (default: True)

    Returns:
        list: List of file entries with path, size, type attributes
    """
    from modal_proto import api_pb2

    def _make_request(metadata):
        request = api_pb2.VolumeListFilesRequest()
        request.volume_id = volume_id
        request.path = path
        request.recursive = recursive

        files_stream = stub.VolumeListFiles(request, metadata=metadata)

        all_entries = []
        for response_chunk in files_stream:
            all_entries.extend(response_chunk.entries)

        return all_entries

    # Generate base metadata and use retry logic
    base_metadata = get_modal_metadata(token_id, token_secret, retry_attempt=0)
    return retry_transient_errors(_make_request, metadata=base_metadata)


def put_file(stub, token_id, token_secret, volume_id, local_path, remote_path, mode=0o644, allow_overwrite=True):
    """Upload a file to a volume with retry logic.

    Protobuf APIs Used:
    1. MountPutFile(MountPutFileRequest) → MountPutFileResponse (content deduplication check)
       Request: MountPutFileRequest { string sha256_hex }
       Response: MountPutFileResponse { bool exists }
    2. MountPutFile(MountPutFileRequest) → MountPutFileResponse (actual upload if needed)
       Request: MountPutFileRequest { string sha256_hex, bytes data }
    3. VolumePutFiles(VolumePutFilesRequest) → Empty (register file in volume)
       Request: VolumePutFilesRequest { string volume_id, repeated MountFile files, bool disallow_overwrite_existing_files }
       MountFile: { string filename, string sha256_hex, optional uint64 size, optional uint32 mode }

    Args:
        stub: gRPC client stub
        token_id: Modal token ID
        token_secret: Modal token secret
        volume_id (str): Volume ID to upload to
        local_path (str): Local file path to upload
        remote_path (str): Remote path in volume (without leading slash)
        mode (int): File permissions (default: 0o644)
        allow_overwrite (bool): Whether to allow overwriting existing files (default: True)

    Returns:
        str: Remote path of uploaded file
    """
    from modal_proto import api_pb2

    # Read file content and calculate hash
    with open(local_path, "rb") as f:
        file_content = f.read()

    sha256_hash = hashlib.sha256(file_content).hexdigest()

    def _check_file_exists(metadata):
        check_request = api_pb2.MountPutFileRequest(sha256_hex=sha256_hash)
        return stub.MountPutFile(check_request, metadata=metadata)

    def _upload_file_content(metadata):
        upload_request = api_pb2.MountPutFileRequest(data=file_content, sha256_hex=sha256_hash)
        return stub.MountPutFile(upload_request, metadata=metadata)

    def _register_file_in_volume(metadata):
        mount_file = api_pb2.MountFile(filename=remote_path, sha256_hex=sha256_hash, mode=mode)

        volume_request = api_pb2.VolumePutFilesRequest(
            volume_id=volume_id, files=[mount_file], disallow_overwrite_existing_files=not allow_overwrite
        )

        return stub.VolumePutFiles(volume_request, metadata=metadata)

    # Generate base metadata and use retry logic for each step
    base_metadata = get_modal_metadata(token_id, token_secret, retry_attempt=0)

    # Check if file already exists (content-based deduplication)
    check_response = retry_transient_errors(_check_file_exists, metadata=base_metadata)

    if not check_response.exists:
        # Upload the file content
        retry_transient_errors(_upload_file_content, metadata=base_metadata)

    # Create the file entry in the volume
    retry_transient_errors(_register_file_in_volume, metadata=base_metadata)
    return remote_path


def get_file(stub, token_id, token_secret, volume_id, remote_path, start=0, length=None):
    """Download a file from a volume with retry logic.

    Protobuf API: VolumeGetFile2(VolumeGetFile2Request) → VolumeGetFile2Response
    Request: VolumeGetFile2Request { string volume_id, string path, uint64 start, uint64 len }
    Response: VolumeGetFile2Response { repeated string get_urls, uint64 size, uint64 start, uint64 len }

    Note: The protobuf API returns signed URLs for download. The actual file content
    is downloaded via HTTP GET from these URLs (not through the gRPC API).

    Args:
        stub: gRPC client stub
        token_id: Modal token ID
        token_secret: Modal token secret
        volume_id (str): Volume ID to download from
        remote_path (str): Remote file path in volume
        start (int): Byte offset to start download from (default: 0)
        length (int): Number of bytes to download (default: None for entire file)

    Returns:
        dict: Response with 'size', 'urls', and 'data' keys
    """
    from modal_proto import api_pb2

    def _make_request(metadata):
        request = api_pb2.VolumeGetFile2Request()
        request.volume_id = volume_id
        request.path = remote_path
        request.start = start
        if length is not None:
            request.len = length

        return stub.VolumeGetFile2(request, metadata=metadata)

    # Generate base metadata and use retry logic
    base_metadata = get_modal_metadata(token_id, token_secret, retry_attempt=0)
    response = retry_transient_errors(_make_request, metadata=base_metadata)

    # Download content from first URL if available
    content = None
    if response.get_urls:
        import requests

        resp = requests.get(response.get_urls[0])
        if resp.status_code == 200:
            content = resp.content

    return {
        "size": response.size,
        "urls": list(response.get_urls),
        "data": content,
        "start": response.start,
        "length": response.len,
    }


def remove_file(stub, token_id, token_secret, volume_id, remote_path, recursive=False):
    """Remove a file or directory from a volume with retry logic.

    Protobuf API: VolumeRemoveFile(VolumeRemoveFileRequest) → Empty
    Request: VolumeRemoveFileRequest { string volume_id, string path, bool recursive }
    Response: Empty (success indicated by no exception)

    Args:
        stub: gRPC client stub
        token_id: Modal token ID
        token_secret: Modal token secret
        volume_id (str): Volume ID to remove from
        remote_path (str): Remote file/directory path to remove
        recursive (bool): Whether to remove directories recursively (default: False)

    Returns:
        bool: True if successful
    """
    from modal_proto import api_pb2

    def _make_request(metadata):
        request = api_pb2.VolumeRemoveFileRequest()
        request.volume_id = volume_id
        request.path = remote_path
        request.recursive = recursive

        stub.VolumeRemoveFile(request, metadata=metadata)
        return True

    # Generate base metadata and use retry logic
    base_metadata = get_modal_metadata(token_id, token_secret, retry_attempt=0)
    return retry_transient_errors(_make_request, metadata=base_metadata)


def copy_files(stub, token_id, token_secret, volume_id, src_paths, dst_path, recursive=False):
    """Copy files within a volume with retry logic.

    Protobuf API: VolumeCopyFiles(VolumeCopyFilesRequest) → Empty
    Request: VolumeCopyFilesRequest { string volume_id, repeated string src_paths, string dst_path, bool recursive }
    Response: Empty (success indicated by no exception)

    Args:
        stub: gRPC client stub
        token_id: Modal token ID
        token_secret: Modal token secret
        volume_id (str): Volume ID to copy within
        src_paths (list): List of source paths to copy
        dst_path (str): Destination path
        recursive (bool): Whether to copy directories recursively (default: False)

    Returns:
        bool: True if successful
    """
    from modal_proto import api_pb2

    def _make_request(metadata):
        request = api_pb2.VolumeCopyFilesRequest()
        request.volume_id = volume_id
        request.src_paths.extend(src_paths)
        request.dst_path = dst_path
        request.recursive = recursive

        stub.VolumeCopyFiles(request, metadata=metadata)
        return True

    # Generate base metadata and use retry logic
    base_metadata = get_modal_metadata(token_id, token_secret, retry_attempt=0)
    return retry_transient_errors(_make_request, metadata=base_metadata)


def create_test_file(size_kb=32):
    """Create a test file for upload demonstration.

    Args:
        size_kb (int): Size of test file in KB (default: 32)

    Returns:
        tuple: (file_path, file_size, sha256_hash)
    """
    content = b"Hello from Modal protobuf demo!\n" * (size_kb * 32)  # Approximately size_kb KB
    temp_file = tempfile.NamedTemporaryFile(mode="wb", delete=False, suffix=".txt", prefix="modal_test_")
    temp_file.write(content)
    temp_file.flush()
    temp_file.close()

    # Calculate SHA256 hash
    with open(temp_file.name, "rb") as f:
        sha256_hash = hashlib.sha256(f.read()).hexdigest()

    return temp_file.name, len(content), sha256_hash


def demo_usage():
    """Demonstrate usage of Modal volume protobuf APIs with retry logic."""
    print("Modal Volume Protobuf API Demo (Self-contained)")
    print("=" * 50)

    # Always check setup first
    if not setup_check():
        return

    # Get credentials from setup check
    token_id, token_secret = load_credentials()

    try:
        import grpc
        from modal_proto import api_pb2_grpc

        # Connect to Modal API
        channel = grpc.secure_channel("api.modal.com:443", grpc.ssl_channel_credentials())
        stub = api_pb2_grpc.ModalClientStub(channel)

        # 1. List volumes
        print("\n1. Listing volumes...")
        volumes = list_volumes(stub, token_id, token_secret)
        print(f"Found {len(volumes)} volumes")

        target_volume_id = None
        target_volume_name = None
        for vol in volumes:
            print(f"   - {vol.label} (ID: {vol.volume_id})")
            if not target_volume_id:  # Use the first available volume
                target_volume_id = vol.volume_id
                target_volume_name = vol.label

        if not target_volume_id:
            print("ERROR: No volumes found. Please create one first with: modal volume create my-volume")
            return

        print(f"Using volume: {target_volume_name} (ID: {target_volume_id})")

        # 2. Create and upload a test file
        print("\n2. Creating and uploading test file...")
        test_file_path, file_size, sha256_hex = create_test_file(size_kb=32)
        remote_path = "test_protobuf_demo.txt"

        try:
            result_path = put_file(
                stub=stub,
                token_id=token_id,
                token_secret=token_secret,
                volume_id=target_volume_id,
                local_path=test_file_path,
                remote_path=remote_path,
                mode=0o644,
                allow_overwrite=True,
            )
            print(f"Upload completed: {result_path} ({file_size:,} bytes)")
        finally:
            # Clean up temp file
            os.unlink(test_file_path)

        # 3. List files to verify upload
        print("\n3. Listing files in volume...")
        files = list_files(stub, token_id, token_secret, target_volume_id, path="/", recursive=True)
        print(f"Found {len(files)} files:")

        # Show our test file first
        test_file_found = False
        for entry in files:
            if entry.type == 1 and entry.path == remote_path:  # FILE type and our test file
                print(f"   [TEST FILE] {entry.path} ({entry.size:,} bytes)")
                test_file_found = True
                break

        # Show a few other files for context
        other_count = 0
        for entry in files:
            if entry.type == 1 and entry.path != remote_path and other_count < 3:  # FILE type, not our test file
                print(f"   - {entry.path} ({entry.size:,} bytes)")
                other_count += 1

        if len(files) > 4:
            print(f"   ... and {len(files) - 4} more files")

        # 4. Download and verify the file
        if test_file_found:
            print(f"\n4. Downloading file: {remote_path}")
            result = get_file(
                stub=stub,
                token_id=token_id,
                token_secret=token_secret,
                volume_id=target_volume_id,
                remote_path=remote_path,
                start=0,
                length=1048576,  # 1MB max
            )

            print("Download result:")
            print(f"   Size: {result['size']:,} bytes")
            print(f"   URLs: {len(result['urls'])}")
            print(f"   Content preview: {result['data'][:50] if result['data'] else 'No data'}")

            if result["data"]:
                print("Download verification successful!")
            else:
                print("WARNING: No content downloaded")

        # 5. Copy the file to demonstrate copy functionality
        print("\n5. Copying file...")
        copy_dst = "test_protobuf_demo_copy.txt"
        try:
            copy_files(
                stub=stub,
                token_id=token_id,
                token_secret=token_secret,
                volume_id=target_volume_id,
                src_paths=[remote_path],
                dst_path=copy_dst,
                recursive=False,
            )
            print(f"File copied to: {copy_dst}")
        except Exception as e:
            print(f"Copy failed: {e}")

        # 6. Clean up - remove test files
        print("\n6. Cleaning up test files...")
        for test_path in [remote_path, copy_dst]:
            try:
                remove_file(
                    stub=stub,
                    token_id=token_id,
                    token_secret=token_secret,
                    volume_id=target_volume_id,
                    remote_path=test_path,
                    recursive=False,
                )
                print(f"Removed: {test_path}")
            except Exception as e:
                print(f"Failed to remove {test_path}: {e}")

        print("\nDemo completed successfully!")
        channel.close()

    except Exception as e:
        print(f"ERROR: {e}")
        import traceback

        traceback.print_exc()


def setup_check():
    """Run setup verification without executing the demo."""
    print("Modal Protobuf Demo - Setup Check")
    print("=" * 35)

    # Check repo path
    if not os.path.exists(MODAL_CLIENT_REPO_PATH):
        print(f"ERROR: Modal client repo not found at: {MODAL_CLIENT_REPO_PATH}")
        print("TO FIX: Update MODAL_CLIENT_REPO_PATH in this script")
        return False
    else:
        print(f"Modal client repo found: {MODAL_CLIENT_REPO_PATH}")

    # Check protobuf files
    if not check_protobuf_files():
        return False

    # Check credentials
    token_id, token_secret = load_credentials()
    if not token_id:
        return False

    print("\nSetup complete! Ready to run the demo.")
    return True


if __name__ == "__main__":
    demo_usage()
