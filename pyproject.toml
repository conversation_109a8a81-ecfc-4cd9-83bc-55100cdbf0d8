[project]
name = "modal-volume-fuse"
version = "0.1.0"
description = "FUSE filesystem for Modal volumes"
requires-python = ">=3.9"

dependencies = [
    "fusepy",
    "grpcio-tools",
    "grpclib",
    "mypy-protobuf",
    "requests"
]

[tool.uv]
dev-dependencies = [
    "invoke",
    "ruff",
    "mypy",
    "modal",
]

[tool.uv.sources]
modal = { path = "./modal-client", editable = true }

# Ruff configuration (adapted from modal-client)
[tool.ruff]
line-length = 120
exclude = [
    '.venv',
    '.git',
    '__pycache__',
    'build',
    'modal-client',
]

[tool.ruff.lint]
select = ['E', 'F', 'W', 'I']
ignore = ['E741']

[tool.ruff.lint.isort]
combine-as-imports = true
known-first-party = ["modal_fuse"]

# MyPy configuration (adapted from modal-client)
[tool.mypy]
python_version = "3.11"
exclude = "build|modal-client"
ignore_missing_imports = true
check_untyped_defs = true
no_strict_optional = true
namespace_packages = true
