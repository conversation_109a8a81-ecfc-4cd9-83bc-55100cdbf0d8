# Modal Volume FUSE

FUSE filesystem for Modal volumes using uv package manager.

## Setup

1. **Install dependencies:**
   ```bash
   uv sync
   ```

2. **Build protobuf files:**
   ```bash
   uv run invoke protoc
   ```

## Dependencies

### Runtime Dependencies
- `fusepy` - FUSE Python bindings
- `grpcio-tools` - Protocol buffer compiler
- `grpclib` - Async gRPC library (provides grpclib protoc plugin)
- `mypy-protobuf` - Type stubs for protobuf
- `requests` - HTTP library for file downloads

### Development Dependencies
- `invoke` - Task runner
- `ruff` - Fast Python linter and formatter
- `mypy` - Static type checker

## Available Commands

### Protobuf Build
```bash
uv run invoke protoc
```
1. Changes to the `modal-client` submodule directory
2. Runs `protoc` to generate Python stubs from `.proto` files
3. Generates both grpcio and grpclib compatible files with mypy type stubs
4. Generates Modal-specific wrapper files (using custom plugin)

Generated files in `modal-client/modal_proto/`:
- `api_pb2.py` - Protocol buffer messages
- `api_pb2_grpc.py` - gRPC service stubs
- `api_grpc.py` - grpclib service stubs
- `*.pyi` - MyPy type stubs

### Code Quality
```bash
# Lint code (check for issues)
uv run invoke lint

# Lint and auto-fix issues
uv run invoke lint --fix

# Format code
uv run invoke format

# Type checking
uv run invoke type-check
```
