#!/usr/bin/env python3

import sys
from pathlib import Path
from invoke import task

# Set working directory to the root of the project
project_root = Path(__file__).parent
modal_client_root = project_root / "modal-client"


@task
def protoc(ctx):
    """Compile protocol buffer files from modal-client submodule."""
    with ctx.cd(str(modal_client_root)):
        protoc_cmd = f"{sys.executable} -m grpc_tools.protoc"
        input_files = "modal_proto/api.proto modal_proto/options.proto"
        py_protoc = f"{protoc_cmd} --python_out=. --grpclib_python_out=. --grpc_python_out=. --mypy_out=. --mypy_grpc_out=."

        print(f"Running: {py_protoc} -I . {input_files}")
        ctx.run(f"{py_protoc} -I . {input_files}")

    print("Protobuf compilation complete!")


@task
def run_script(ctx, name):
    """Run a script from the scripts/ directory."""
    script_path = project_root / "scripts" / f"{name}.py"
    if not script_path.exists():
        print(f"Script not found: {script_path}")
        return

    print(f"Running script: {script_path}")
    ctx.run(f"uv run python {script_path}", pty=True)
