# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.ruff_cache/

# Virtual environments
.venv/
venv/
ENV/
env/

# uv
uv.lock

# Generated protobuf files (in modal-client submodule)
modal-client/modal_proto/*_pb2.py
modal-client/modal_proto/*_pb2_grpc.py
modal-client/modal_proto/*_grpc.py
modal-client/modal_proto/*.pyi

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
.cache/
