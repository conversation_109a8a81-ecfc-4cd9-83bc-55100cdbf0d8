name: Check

on:
  push:
    branches:
      - main
  pull_request:

# Cancel previous runs of the same PR but do not cancel previous runs on main
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

jobs:
  lint:
    name: Ruff linting
    runs-on: ubuntu-24.04

    steps:
      - uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3

      - uses: ./.github/actions/setup-cached-python
        with:
          version: "3.10"

      - run: inv lint

      - run: inv lint-protos

  type_check:
    name: Static type checks
    runs-on: ubuntu-24.04

    steps:
      - uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3

      - uses: ./.github/actions/setup-cached-python
        with:
          version: "3.10"

      - run: inv protoc

      - run: pip install -e .  # gets all dependencies and the package itself into python env

      - name: Build type stubs
        run: inv type-stubs

      - run: inv type-check

  check-copyright:
    name: Check copyright
    runs-on: ubuntu-24.04

    steps:
      - uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3

      - uses: ./.github/actions/setup-cached-python
        with:
          version: "3.10"

      - run: inv check-copyright
