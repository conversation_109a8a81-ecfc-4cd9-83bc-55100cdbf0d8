name: Docs

on:
  push:
    branches:
      - main
  pull_request:

# Cancel previous runs of the same PR but do not cancel previous runs on main
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

jobs:
  doc-test:
    name: Doc generation tests
    timeout-minutes: 5
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3

      - uses: ./.github/actions/setup-cached-python

      - name: Build protobuf
        run: inv protoc

      - name: Install package + deps
        run: pip install -e .  # Makes sure doc generation doesn't break on client imports etc.

      - name: Generate reference docs
        run: python -m modal_docs.gen_reference_docs reference_docs_output

      - name: Generate CLI docs
        run: python -m modal_docs.gen_cli_docs cli_docs
