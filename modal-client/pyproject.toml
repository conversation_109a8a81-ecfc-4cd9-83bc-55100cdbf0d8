[build-system]
requires = ["setuptools~=77.0.3", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "modal"
description = "Python client library for Modal"
readme = "README.md"
dynamic = ["version"]
requires-python = ">=3.9"
license = {text = "Apache-2.0"}

authors = [
    { name = "Modal Labs", email = "<EMAIL>" }
]
dependencies = [
    "aiohttp",
    "certifi",
    "click~=8.1",
    "grpclib>=0.4.7,<0.4.9",
    "protobuf>=3.19,<7.0,!=4.24.0",
    "rich>=12.0.0",
    "synchronicity~=0.10.1",
    "toml",
    "typer>=0.9",
    "types-certifi",
    "types-toml",
    "watchfiles",
    "typing_extensions~=4.6"
]
keywords = ["modal", "client", "cloud", "serverless", "infrastructure"]
classifiers = [
    "Topic :: System :: Distributed Computing",
    "Operating System :: OS Independent",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3"
]

[project.urls]
Homepage = "https://modal.com"
Source = "https://github.com/modal-labs/modal-client"
Documentation = "https://modal.com/docs"
"Issue Tracker" = "https://github.com/modal-labs/modal-client/issues"

[project.scripts]
modal = "modal.__main__:main"

[tool.setuptools.packages.find]
include = ["modal", "modal.*", "modal_docs", "modal_docs.*", "modal_version", "modal_proto"]
exclude = ["test*", "modal_global_objects"]

[tool.setuptools.package-data]
modal = ["builder/*.md", "builder/*.txt", "builder/*.json", "py.typed", "*.pyi"]
modal_proto = ["*.proto", "py.typed", "*.pyi"]

[tool.setuptools.dynamic]
version = {attr = "modal_version.__version__"}

[tool.mypy]
python_version = "3.11"
exclude = "build"
ignore_missing_imports = true
check_untyped_defs = true
no_strict_optional = true
namespace_packages = true

[[tool.mypy.overrides]]
module = [
    "modal/_vendor/cloudpickle",
    "modal/_vendor/tblib",
    "modal/_vendor/a2wsgi_wsgi",
]
ignore_errors = true

[tool.pytest.ini_options]
timeout = 300
addopts = "--ignore=modal/cli/programs"
filterwarnings = [
    "error::DeprecationWarning",
    "ignore:Type google._upb.*MapContainer uses PyType_Spec.*Python 3.14:DeprecationWarning",
    "error::modal.exception.DeprecationError",
    "ignore::DeprecationWarning:pytest.*:",
    "ignore::DeprecationWarning:pkg_resources.*:",
    "ignore::DeprecationWarning:google.rpc.*:",
    "ignore:.*pkg_resources.*:DeprecationWarning::",
]

[tool.ruff]
extend-include = ["*.pyi"]
exclude = [
    '.venv',
    '.git',
    '__pycache__',
    'proto',
    'build',
    'modal_proto',
    'modal/_vendor',
]
line-length = 120
lint.ignore = ['E741']
lint.select = ['E', 'F', 'W', 'I']

[tool.ruff.lint.per-file-ignores]
"*_test.py" = ['E712']
"test/supports/notebooks/*.py" = ['E402']

[tool.ruff.lint.isort]
combine-as-imports = true
known-first-party = [
    "modal",
    "modal_global_objects",
    "modal_proto",
    "modal_version",
]
extra-standard-library = ["pytest"]

[tool.pyright]
reportUnnecessaryComparison = true
