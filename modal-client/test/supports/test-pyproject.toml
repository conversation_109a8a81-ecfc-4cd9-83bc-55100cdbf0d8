[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[tool.mypy]
python_version = "3.9"
exclude = "build"
ignore_missing_imports = true
check_untyped_defs = true
no_strict_optional = true
namespace_packages = true

[project]
name = "foo"
description = "bar"
requires-python = ">=3.9"
dependencies = ["banana >=1.2.0", "potato >=0.1.0"]

[project.optional-dependencies]
dev = ["linting-tool >=0.0.0"]
test = ["pytest >=1.2.0"]
doc = ["mkdocs >=1.4.2"]
