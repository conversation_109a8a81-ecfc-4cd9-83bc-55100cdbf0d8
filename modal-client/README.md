# Modal Python Library

[![PyPI Version](https://img.shields.io/pypi/v/modal.svg)](https://pypi.org/project/modal/)
[![License](https://img.shields.io/badge/license-apache_2.0-darkviolet.svg)](https://github.com/modal-labs/modal-client/blob/master/LICENSE)
[![Tests](https://github.com/modal-labs/modal-client/actions/workflows/ci-cd.yml/badge.svg)](https://github.com/modal-labs/modal-client/actions/workflows/ci-cd.yml)
[![Slack](https://img.shields.io/badge/slack-join-blue.svg?logo=slack)](https://modal.com/slack)

The [Modal](https://modal.com/) Python library provides convenient, on-demand
access to serverless cloud compute from Python scripts on your local computer.

## Documentation

See the [online documentation](https://modal.com/docs/guide) for many
[example applications](https://modal.com/docs/examples),
a [user guide](https://modal.com/docs/guide), and the detailed
[API reference](https://modal.com/docs/reference).

## Installation

**This library requires Python 3.9 – 3.13.**

Install the package with `pip`:

```bash
pip install modal
```

You can create a Modal account (or link your existing one) directly on the
command line:

```bash
python3 -m modal setup
```

## Support

For usage questions and other support, please reach out on the
[Modal Slack](https://modal.com/slack).
